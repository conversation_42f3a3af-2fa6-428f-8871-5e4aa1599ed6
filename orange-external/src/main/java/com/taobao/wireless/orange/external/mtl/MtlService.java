package com.taobao.wireless.orange.external.mtl;

import com.alibaba.emas.mtl4.commons.utils.Assert;
import com.alibaba.emas.mtl4.commons.utils.httpclient.HttpMethod;
import com.alibaba.emas.mtl4.commons.utils.httpclient.RequestMessage;
import com.alibaba.emas.mtl4.commons.utils.httpclient.ResponseMessage;
import com.alibaba.fastjson.JSON;
import com.alibaba.mtl.generic.open.sdk.consumer.ApiClient;
import com.alibaba.mtl.generic.open.sdk.consumer.ClientConfiguration;
import com.alibaba.mtl.open.protocol.entry.EntryToken;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.external.mtl.model.MtlBaseResult;
import com.taobao.wireless.orange.external.mtl.model.MtlModule;
import com.taobao.wireless.orange.external.mtl.model.MtlPaginationResult;
import com.taobao.wireless.orange.external.mtl.model.MtlResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.lang.reflect.Type;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class MtlService {
    @Value("${mtl.endpoint}")
    private String endpoint;
    @Value("${mtl.accessKey}")
    private String accessKey;
    @Value("${mtl.accessSecret}")
    private String accessSecret;

    private ApiClient apiClient;

    public void init() throws URISyntaxException {
        Assert.notBlank(endpoint, "endpoint can not be blank");
        Assert.notBlank(accessKey, "accessKey can not be blank");
        Assert.notBlank(accessSecret, "accessSecret can not be blank");

        EntryToken entryToken = EntryToken.builder().identifier(accessKey).token(accessSecret).build();
        this.apiClient = new ApiClient(accessKey, new URI(endpoint), entryToken, new ClientConfiguration());
    }

    /**
     * 搜索模块列表
     *
     * @param appKey
     * @param keyword
     * @param pagination
     * @return
     */
    public PaginationResult<MtlModule> queryModules(String appKey, String keyword, Pagination pagination) {
        if (StringUtils.isBlank(appKey) || StringUtils.isBlank(keyword)) {
            throw new CommonException(ExceptionEnum.PARAM_INVALID, "appKey 和 keyword 均不能为空");
        }

        String api = "/dev/api/v1/sre/" + appKey + "/modules";
        Map<String, String> parameters = new HashMap<>();
        parameters.put("keyword", keyword);
        if (pagination != null) {
            if (pagination.getPageNum() > 0) {
                // mtl 页码从 0 开始
                parameters.put("pageNum", String.valueOf(pagination.getPageNum() - 1));
            }
            if (pagination.getPageSize() > 0) {
                parameters.put("pageSize", String.valueOf(pagination.getPageSize()));
            }
        }

        RequestMessage request = RequestMessage.builder().method(HttpMethod.GET).path(api).parameters(parameters).build();
        Type type = new TypeToken<MtlPaginationResult<MtlModule>>() {
        }.getType();

        MtlPaginationResult<MtlModule> mtlResult = (MtlPaginationResult<MtlModule>) requestMtl(request, type);

        var result = new PaginationResult<MtlModule>();
        result.setCurrent(mtlResult.getPageNum().longValue());
        result.setTotal(mtlResult.getTotalCount().longValue());
        result.setSize(mtlResult.getPageSize().longValue());
        result.setData(mtlResult.getData());
        return result;
    }

    /**
     * 根据模块 ID 批量查询模块详情
     *
     * @param moduleIds
     * @return
     */
    public Map<Long, MtlModule> getModulesByModuleIds(List<Long> moduleIds) {
        if (CollectionUtils.isEmpty(moduleIds)) {
            return new HashMap<>(0);
        }

        String api = "/dev/api/v1/sre/modules";

        String ids = moduleIds.stream().map(i -> i.toString()).collect(Collectors.joining(","));
        Map<String, String> parameters = Collections.singletonMap("ids", ids);

        RequestMessage request = RequestMessage.builder().method(HttpMethod.GET).path(api).parameters(parameters).build();
        Type type = new TypeToken<MtlResult<List<MtlModule>>>() {
        }.getType();

        MtlResult<List<MtlModule>> result = (MtlResult<List<MtlModule>>) requestMtl(request, type);

        return result.getData().stream().collect(Collectors.toMap(MtlModule::getModuleId, module -> module));
    }

    /**
     * 调用 MTL 服务，并对异常结果进行处理
     *
     * @param request
     * @param type
     * @return
     */
    private MtlBaseResult requestMtl(RequestMessage request, Type type) {
        ResponseMessage responseMessage = this.apiClient.sendRequest(request);
        if (responseMessage.getStatusCode() != 200) {
            throw new CommonException(ExceptionEnum.MTL_ERROR, "调用 MTL 接口失败，状态码：" + responseMessage.getStatusCode());
        }
        String resultStr = responseMessage.getJson();
        try {
            MtlBaseResult result = new Gson().fromJson(resultStr, type);
            if (!result.getSuccess()) {
                log.error("调用 MTL 接口失败，入参：{}，结果：{}", JSON.toJSONString(request), resultStr);
                throw new CommonException(ExceptionEnum.MTL_ERROR, "调用 MTL 接口失败: " + result.getErrorMsg());
            }
            return result;
        } catch (JsonSyntaxException e) {
            log.error("解析 MTL 返回结果失败，入参：" + JSON.toJSONString(request) + "，结果：" + resultStr, e);
            throw new CommonException(ExceptionEnum.MTL_ERROR, "解析结果失败");
        }
    }
}
