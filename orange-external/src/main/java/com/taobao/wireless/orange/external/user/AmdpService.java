package com.taobao.wireless.orange.external.user;

import com.alibaba.ihr.amdplatform.common.util.AmdpResponseUtil;
import com.alibaba.ihr.amdplatform.service.api.AmdpDataQueryService;
import com.alibaba.ihr.amdplatform.service.dto.QueryResultDTO;
import com.alibaba.ihr.amdplatform.service.dto.ResultDTO;
import com.alibaba.ihr.amdplatform.service.param.AuthParam;
import com.alibaba.ihr.amdplatform.service.param.DataQueryParam;
import com.taobao.wireless.orange.common.exception.CommonException;
import com.taobao.wireless.orange.common.exception.ExceptionEnum;
import com.taobao.wireless.orange.common.model.User;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class AmdpService {
    @Autowired
    private AmdpDataQueryService amdpDataQueryService;

    @Value("${amdp.api.emp.id}")
    private Long amdpApiEmpId;
    @Value("${amdp.app.key}")
    private String appKey;
    @Value("${amdp.app.secret}")
    private String appSecret;

    public Map<String, User> queryUserByWorkNoList(List<String> workNoList) {
        DataQueryParam queryParam = AmdpUserUtils.genQueryParam(amdpApiEmpId, AmdpUserQueryKeyEnum.WORK_NO_LIST, workNoList);
        List<AmdpUser> amdpUsers = queryDataSet(queryParam);
        return CollectionUtils.isNotEmpty(amdpUsers) ? AmdpUserUtils.convertMap(amdpUsers) : null;
    }

    private List<AmdpUser> queryDataSet(DataQueryParam queryParam) {
        ResultDTO<QueryResultDTO> resultDTO = amdpDataQueryService.queryDataSet(new AuthParam(appKey, appSecret), queryParam);
        if (resultDTO == null) {
            throw new CommonException(ExceptionEnum.AMDP_ERROR);
        } else if (!resultDTO.isSuccess()) {
            String errorMsg = String.format("错误原因：%s(%s,%s)", resultDTO.getErrorMessage(), resultDTO.getStatus(), resultDTO.getErrorCode());
            throw new CommonException(ExceptionEnum.AMDP_ERROR, errorMsg);
        }
        return AmdpResponseUtil.convertCombineResultToBeanList(resultDTO, AmdpUser.class);
    }
}
