package com.taobao.wireless.orange.service;

import com.taobao.wireless.orange.common.annotation.AttributeValidate;
import com.taobao.wireless.orange.common.model.Pagination;
import com.taobao.wireless.orange.common.model.PaginationResult;
import com.taobao.wireless.orange.common.model.Result;
import com.taobao.wireless.orange.common.util.BeanUtil;
import com.taobao.wireless.orange.common.util.Pipe;
import com.taobao.wireless.orange.manager.NamespaceManager;
import com.taobao.wireless.orange.manager.model.OONamespaceBO;
import com.taobao.wireless.orange.service.model.NamespaceCreateDTO;
import com.taobao.wireless.orange.service.model.NamespaceDTO;
import com.taobao.wireless.orange.service.model.NamespaceQueryDTO;
import com.taobao.wireless.orange.service.model.NamespaceUpdateDTO;
import com.taobao.wireless.orange.manager.util.PageUtil;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 命名空间操作接口。
 */
@Service
public class NamespaceService {

    @Autowired
    private NamespaceManager namespaceManager;

    /**
     * 获取所有命名空间的列表。
     *
     * @return Namespace对象列表
     */
    @AttributeValidate
    public PaginationResult<NamespaceDTO> query(@NotNull(message = "命名空间查询条件不能为空") NamespaceQueryDTO namespaceQueryDTO, Pagination pagination) {
        return Pipe.of(namespaceQueryDTO)
                .map(q -> BeanUtil.createFromProperties(q, OONamespaceBO.class))
                .map(q -> namespaceManager.query(q, pagination))
                .map(r -> PageUtil.convert(r, NamespaceDTO.class))
                .get();
    }

    /**
     * 创建新的命名空间。
     *
     * @param updateNamespace 要创建的Namespace对象
     */
    @AttributeValidate
    public Result<String> create(@NotNull(message = "命名空间对象不能为空") NamespaceCreateDTO updateNamespace) {
        return Pipe.of(updateNamespace)
                .map(n -> BeanUtil.createFromProperties(n, OONamespaceBO.class))
                .map(namespaceManager::create)
                .map(Result::new)
                .get();
    }

    /**
     * 获取指定命名空间的详细信息。
     *
     * @param namespaceId 命名空间ID
     * @return 包含详细信息的Namespace对象
     */
    @AttributeValidate
    public Result<NamespaceDTO> getByNamespaceId(@NotBlank(message = "命名空间ID不能为空") String namespaceId) {
        return Pipe.of(namespaceManager.getByNamespaceId(namespaceId))
                .map(ns -> BeanUtil.createFromProperties(ns, NamespaceDTO.class))
                .map(Result::new)
                .get();
    }

    /**
     * 更新现有的命名空间信息。
     *
     * @param namespace 更新后的Namespace对象
     */
    @AttributeValidate
    public Result<Void> update(@NotNull(message = "命名空间对象不能为空") NamespaceUpdateDTO namespace) {
        Pipe.of(namespace)
                .map(n -> BeanUtil.createFromProperties(n, OONamespaceBO.class))
                .map(namespaceManager::update);
        return Result.success();
    }
}
