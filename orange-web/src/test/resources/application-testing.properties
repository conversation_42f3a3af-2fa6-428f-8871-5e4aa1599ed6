project.name=orange
server.port=7001
management.server.port=7002

#spring.tair.user-name=9ed882569b0c433a
#spring.tair.dynamic-config=true

#hsf
spring.hsf.group=HSF
spring.hsf.version=1.0.0.DAILY
spring.hsf.timeout=2000

spring.eagleeye.mdc-updater=slf4j
spring.eagleeye.enabled=true

spring.metaq.producer.producer-group=panodra-boot-metaq-sample
#spring.metaq.consumer.consumer-group=CID-ORANGE
#spring.metaq.consumer.topic=wmcc_diamond_beta_publish
#spring.metaq.consumer.subExpression=*
#spring.metaq.consumer.message-listener-ref=metaqMessageListener

spring.metaq.consumers[0].consumer-group=CID-ORANGE-WMCC
spring.metaq.consumers[0].topic=wmcc_diamond_beta_publish
spring.metaq.consumers[0].sub-expression=*
spring.metaq.consumers[0].message-listener-ref=metaqMessageListener
spring.buc.app-name=union-data-op-web

spring.schedulerx2.domainName=schedulerx2.taobao.net
spring.schedulerx2.namespace=system_namespace
spring.schedulerx2.groupId=orange
spring.schedulerx2.appKey=WPtgg6giacpteU5cSUhMscw

# BUC
spring.buc.app-code=cf2c3892f6c2403e81d40f42026a2b09
spring.buc.filter.url-patterns=/*
spring.buc.login-env=daily

logging.level.root=DEBUG

orange.cdn.domain=o-config-testing.oss-rg-china-mainland.aliyuncs.com

orange.oss.accessKeyId=LTAI5tCWxJbRkQ3xzRxeqn4G
orange.oss.secretAccessKey=******************************
orange.oss.bucketName=o-config-testing
orange.oss.endpoint=oss-rg-china-mainland.aliyuncs.com

amdp.app.key=orange
amdp.hsf.group=HSF
amdp.hsf.version=1.0.0.DAILY
amdp.app.secret=test
amdp.api.emp.id=1615

mtl.accessKey=orange
mtl.accessSecret=ade2d56eca6f1fbba90033dec47dd908
mtl.endpoint=https://open.mtl4.alibaba-inc.com
